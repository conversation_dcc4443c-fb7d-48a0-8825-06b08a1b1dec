from app.gpt_models.gpt_prompts import GptPromptPicker
import json
import asyncio
import os
import time
import traceback
from datetime import datetime

import pandas as pd
import openai
from openai import OpenAI
from google import genai
from google.genai import types
import dotenv
from sqlmodel import Session, select
from app.database import engine
from app.models.db_models import WebsiteUrls, Website, ScrapeRequestTracker

# Load environment variables from .env
dotenv.load_dotenv('/home/<USER>/Desktop/final/.env')

priority_categories = {
    "home_page",
    "catalogue",
    "products",
    "services",
    "about_us",
    "terms_and_condition",
    "returns_cancellation_exchange",
    "privacy_policy",
    "shipping_delivery",
    "contact_us",
    "pricing",
    "faq",
    "instagram_page",
    "facebook_page",
    "youtube_page",
    "twitter_page",
    "linkedin_page",
    "pinterest_page",
}

exclude_mccs = [9999]

# Simple logger class to replace the complex logger
# Import the proper structured logger from the application
from app.utils.logger import ConsoleLogger

class SimpleLogger:
    """Wrapper to maintain backward compatibility while using proper structured logging"""
    def __init__(self, analysis_id=None):
        self.console_logger = ConsoleLogger(analysis_id or "url_classification")

    def info(self, message, extra=None):
        self.console_logger.info(message, extra)

    def warning(self, message, extra=None):
        self.console_logger.warning(message, extra)

    def error(self, message, error=None):
        self.console_logger.error(message, error)

class urlclassification_service:
    """Service class for URL classification"""
    
    def __init__(self, website=None, scrape_request_ref_id=None, org_id="default"):
        # Removed website_id dependency - using scrape_request_ref_id as primary identifier
        self.website = website
        self.scrape_request_ref_id = scrape_request_ref_id
        self.org_id = org_id
        self.logger = SimpleLogger(f"url_classification_{scrape_request_ref_id}")
    
    async def process_classification(self):
        """Main method to process URL classification"""
        try:
            # Get URLs for the website from database
            urls_depth_1, urls_depth_2 = self.get_urls_from_db()
            
            # Perform soft classification
            output_df, website_urls_dict = self.soft_classify_urls(
                urls_depth_1,
                urls_depth_2,
                self.logger,
                self.scrape_request_ref_id,
                self.org_id
            )
            
            # Filter and set home_page URLs
            # Filter out categories that will be sent ahead
            categories_to_filter = [
                "home_page", "about_us", "terms_and_condition", 
                "returns_cancellation_exchange", "privacy_policy", 
                "shipping_delivery", "contact_us", "catalogue",
            ]

            filtered_website_urls_dict = {}
            # Remove filtered categories from website_urls_dict
            for category in categories_to_filter:
                if category in website_urls_dict:
                    filtered_category = website_urls_dict[category]
                    filtered_website_urls_dict[category] = filtered_category
            website_urls_dict = filtered_website_urls_dict

            self.logger.info(f"Filtered website URLs dictionary: {website_urls_dict}")

            # Perform hard classification
            verified_urls_dict = self.hard_classify_urls(website_urls_dict)
            
            # Update database with classification results
            self.update_db_with_results(output_df)
            
            return {
                "status": "COMPLETED",
                "scrape_request_ref_id": self.scrape_request_ref_id,
                "website": self.website,
                "urls_classified": len(output_df)
            }
        except Exception as e:
            self.logger.error(f"Error in URL classification process: {str(e)}", error=e)
            traceback.print_exc()
            return {
                "status": "FAILED",
                "error": str(e),
                "scrape_request_ref_id": self.scrape_request_ref_id,
                "website": self.website
            }
    
    def get_urls_from_db(self):
        """Get URLs from database using scrape_request_ref_id"""
        urls_depth_1 = []
        urls_depth_2 = []
        
        with Session(engine) as session:
            # Get URLs at depth 1
            depth_1_query = select(WebsiteUrls).where(
                WebsiteUrls.scrape_request_ref_id == self.scrape_request_ref_id,
                WebsiteUrls.depth == 1
            )
            for url in session.exec(depth_1_query):
                urls_depth_1.append(url.url)
            
            # Get URLs at depth 2
            depth_2_query = select(WebsiteUrls).where(
                WebsiteUrls.scrape_request_ref_id == self.scrape_request_ref_id,
                WebsiteUrls.depth == 2
            )
            for url in session.exec(depth_2_query):
                urls_depth_2.append(url.url)
        
        self.logger.info(f"Retrieved {len(urls_depth_1)} URLs at depth 1 and {len(urls_depth_2)} URLs at depth 2")
        return urls_depth_1, urls_depth_2
    
    def update_db_with_results(self, output_df):
        """Update database with soft classification results using scrape_request_ref_id"""
        # Only update database if we have a valid scrape_request_ref_id
        if not self.scrape_request_ref_id:
            self.logger.info("Skipping database update - no scrape_request_ref_id provided")
            return False

        try:
            with Session(engine) as session:
                # OPTIMIZED: Get all URLs for this scrape request in one query
                all_urls_query = select(WebsiteUrls).where(
                    WebsiteUrls.scrape_request_ref_id == self.scrape_request_ref_id
                )
                all_url_records = session.exec(all_urls_query).all()

                # Create URL mapping for fast lookup
                url_record_map = {url_record.url: url_record for url_record in all_url_records}

                self.logger.info(f"Retrieved {len(all_url_records)} URL records for soft classification update")

                # Update URLs based on the dataframe results - no individual queries needed
                updates_made = 0
                for _, row in output_df.iterrows():
                    url = row['url']
                    soft_class = row['soft_class']
                    priority_url = row['priority_url']

                    # Find URL record from our pre-loaded mapping (no database query)
                    url_record = url_record_map.get(url)

                    if url_record:
                        # Update soft classification information
                        url_record.soft_class = json.dumps(soft_class)
                        url_record.priority_url = priority_url
                        updates_made += 1

                # Commit updates
                session.commit()
                self.logger.info(f"Updated {updates_made} database records with soft classification out of {len(output_df)} URLs")
                return True

        except Exception as e:
            self.logger.error(f"Error updating database with soft classification results: {e}")
            return False

    def update_db_with_hard_classification(self, hard_classified_urls):
        """Update database with hard classification results using scrape_request_ref_id"""
        # Only update database if we have a valid scrape_request_ref_id
        if not self.scrape_request_ref_id:
            self.logger.info("Skipping hard classification database update - no scrape_request_ref_id provided")
            return False

        if not hard_classified_urls or not isinstance(hard_classified_urls, dict):
            self.logger.warning("Invalid hard classification results provided for database update")
            return False

        try:
            with Session(engine) as session:
                # Get all URLs for this scrape request in one query
                all_urls_query = select(WebsiteUrls).where(
                    WebsiteUrls.scrape_request_ref_id == self.scrape_request_ref_id
                )
                all_url_records = session.exec(all_urls_query).all()

                # Create URL mapping for fast lookup
                url_record_map = {url_record.url: url_record for url_record in all_url_records}

                self.logger.info(f"Retrieved {len(all_url_records)} URL records for hard classification update")

                # Track statistics
                updates_made = 0
                total_hard_classified_urls = 0

                # Process each category and its URLs from hard classification results
                for category, urls in hard_classified_urls.items():
                    if isinstance(urls, list):
                        total_hard_classified_urls += len(urls)

                        for url in urls:
                            if isinstance(url, str):
                                # Find URL record from our pre-loaded mapping
                                url_record = url_record_map.get(url)

                                if url_record:
                                    # Get existing hard classification or initialize empty list
                                    try:
                                        existing_hard_class = json.loads(url_record.hard_class) if url_record.hard_class else []
                                        if not isinstance(existing_hard_class, list):
                                            existing_hard_class = []
                                    except (json.JSONDecodeError, TypeError):
                                        existing_hard_class = []

                                    # Add category to hard classification if not already present
                                    if category not in existing_hard_class:
                                        existing_hard_class.append(category)

                                    # Update hard classification
                                    url_record.hard_class = json.dumps(existing_hard_class)
                                    updates_made += 1
                                else:
                                    self.logger.warning(f"URL not found in database for hard classification update: {url}")

                # Commit updates
                session.commit()

                self.logger.info(f"Updated {updates_made} database records with hard classification")
                self.logger.info(f"Hard classification summary: {total_hard_classified_urls} total URLs across {len(hard_classified_urls)} categories")
                return True

        except Exception as e:
            self.logger.error(f"Error updating database with hard classification results: {e}")
            return False
            

    def update_db_with_hard_classification(self, hard_classified_urls):
        """Update database with hard classification results using scrape_request_ref_id"""
        # Only update database if we have a valid scrape_request_ref_id
        if not self.scrape_request_ref_id:
            self.logger.info("Skipping hard classification database update - no scrape_request_ref_id provided")
            return
            
        if not hard_classified_urls or not isinstance(hard_classified_urls, dict):
            self.logger.warning("Invalid hard classification results provided for database update")
            return
            
        with Session(engine) as session:
            # Get all URLs for this scrape request in one query
            all_urls_query = select(WebsiteUrls).where(
                WebsiteUrls.scrape_request_ref_id == self.scrape_request_ref_id
            )
            all_url_records = session.exec(all_urls_query).all()
            
            # Create URL mapping for fast lookup
            url_record_map = {url_record.url: url_record for url_record in all_url_records}
            
            self.logger.info(f"Retrieved {len(all_url_records)} URL records for hard classification update")
            
            # Track statistics
            updates_made = 0
            total_hard_classified_urls = 0
            
            # Process each category and its URLs from hard classification results
            for category, urls in hard_classified_urls.items():
                if isinstance(urls, list):
                    total_hard_classified_urls += len(urls)
                    
                    for url in urls:
                        if isinstance(url, str):
                            # Find URL record from our pre-loaded mapping
                            url_record = url_record_map.get(url)
                            
                            if url_record:
                                # Get existing hard classification or initialize empty list
                                try:
                                    existing_hard_class = json.loads(url_record.hard_class) if url_record.hard_class else []
                                    if not isinstance(existing_hard_class, list):
                                        existing_hard_class = []
                                except (json.JSONDecodeError, TypeError):
                                    existing_hard_class = []
                                
                                # Add category to hard classification if not already present
                                if category not in existing_hard_class:
                                    existing_hard_class.append(category)
                                
                                # Update hard classification
                                url_record.hard_class = json.dumps(existing_hard_class)
                                updates_made += 1
                            else:
                                self.logger.warning(f"URL not found in database for hard classification update: {url}")
            
            # Commit updates
            session.commit()
            
            self.logger.info(f"Updated {updates_made} database records with hard classification")
            self.logger.info(f"Hard classification summary: {total_hard_classified_urls} total URLs across {len(hard_classified_urls)} categories")
            
            # Log category breakdown
            category_summary = {category: len(urls) for category, urls in hard_classified_urls.items() if isinstance(urls, list)}
            self.logger.info(f"Hard classification category breakdown: {category_summary}")
    
    def load_json_data(self, json_file_path):
        """Load and parse the JSON file"""
        try:
            if not os.path.exists(json_file_path):
                self.logger.error(f"JSON file not found: {json_file_path}")
                return None
            
            with open(json_file_path, 'r') as f:
                data = json.load(f)
            
            return data
        except Exception as e:
            self.logger.error(f"Error loading JSON file: {e}")
            return None
    
    def prepare_dataframe(self, website, urls1, urls2):
        """Prepare DataFrame with URL data"""
        urls1 = [url if isinstance(url, str) else str(url) for url in urls1]
        urls2 = [url if isinstance(url, str) else str(url) for url in urls2]

        dictionary_1 = {i: url for i, url in enumerate(urls1)}
        dictionary_2 = {i: url for i, url in enumerate(urls2)}

        df_output = pd.DataFrame(
            {
                "website": [website],
                "url_1": [urls1],  # List of URLs at depth 1
                "url_2": [urls2],  # List of URLs at depth 2
                "dictionary_1": [dictionary_1],  # Dictionary of URLs at depth 1
                "dictionary_2": [dictionary_2],  # Dictionary of URLs at depth 2
            }
        )

        return df_output

    def get_openai_response(self, prompt, model_name="gpt-4o"):
        """Get response from OpenAI API"""
        max_retries = 3
        retry_count = 0
        
        # Initialize OpenAI client
        client = OpenAI(api_key=os.getenv("OPENAI_API_KEY"))
        
        while retry_count < max_retries:
            try:
                # Format messages properly as an array of message objects
                messages = [
                     {"role": "user", "content": prompt}
                ]
                
                self.logger.info(f"Sending request to OpenAI API using model {model_name}...")
                self.logger.info(f"Processing URLs for classification. This may take some time...")
                
                response = client.chat.completions.create(
                    model=model_name,
                    messages=messages,
                    temperature=0,
                    top_p=0.95,
                    max_tokens=6000,
                    seed=0
                )
                
                self.logger.info(f"Usage: {response.usage}")
                time.sleep(1)  # Rate limiting
                
                # Check if we got a valid response
                if response.choices and len(response.choices) > 0:
                    message = response.choices[0].message
                    if message and message.content:
                        return message.content.strip()
                    else:
                        self.logger.warning(f"Empty response content, retrying ({retry_count+1}/{max_retries})...")
                        retry_count += 1
                        time.sleep(3)
                else:
                    self.logger.warning(f"No choices in response, retrying ({retry_count+1}/{max_retries})...")
                    retry_count += 1
                    time.sleep(3)
                    
            except Exception as e:
                self.logger.error(f"Error in OpenAI API call: {e}, retrying ({retry_count+1}/{max_retries})...")
                retry_count += 1
                time.sleep(3)  # Wait before retrying
        
        # If we've exhausted all retries
        return "No response from OpenAI API after multiple attempts"

    def get_gemini_response_hard_classification(self, prompt, model_name="gemini-2.5-flash"):
        """Get response from Gemini API for hard classification using optimized model selection"""
        from app.gpt_models.gemini_model_wrapper.gemeni_utils import get_optimized_gemini_response_for_task
        
        try:
            # This is post-soft classification, use faster non-reasoning model
            response = get_optimized_gemini_response_for_task(
                prompt,
                task_type="post_soft_classification",
                custom_model=model_name
            )
            
            # Check if response indicates an error
            if response.startswith("Error:") or response.startswith("No response from Gemini"):
                self.logger.error(f"Gemini hard classification failed: {response}")
                return "No response from Gemini API after multiple attempts"
            
            return response
            
        except Exception as e:
            self.logger.error(f"Error in centralized Gemini call: {e}")
            return "No response from Gemini API after multiple attempts"

    def parse_openai_response(self, response_text):
        """Parse the JSON response from OpenAI"""
        try:
            # Try to find JSON in the response
            response_text = response_text.strip()
            
            # Remove markdown code blocks if present
            if response_text.startswith("```json"):
                response_text = response_text[7:]
            if response_text.endswith("```"):
                response_text = response_text[:-3]
            response_text = response_text.strip()
            
            # Try to parse as JSON
            policy_urls_dict = json.loads(response_text)
            return policy_urls_dict
        except json.JSONDecodeError as e:
            self.logger.error(f"Error parsing JSON response: {e}")
            self.logger.error(f"Response text: {response_text}")
            return None

    def convert_indices_to_urls(self, indices_dict, url_mapping):
        """Convert integer indices to actual URLs using the provided mapping dictionary."""
        urls_dict = {}
        
        for category, indices in indices_dict.items():
            urls_dict[category] = []
            for idx in indices:
                if isinstance(idx, int) and idx in url_mapping:
                    urls_dict[category].append(url_mapping[idx])
                else:
                    self.logger.warning(f"Warning: Index {idx} not found in URL mapping or not an integer")
        
        return urls_dict

    def hard_classify_urls(self, website_urls_dict):
        """Perform hard classification verification using Gemini - POLICY URLS ONLY"""
        self.logger.info("Starting hard classification verification for POLICY URLs only", {"website": self.website})

        try:
            # FIXED: Only process POLICY categories in hard classification, exclude social media
            policy_categories = ["terms_and_condition", "returns_cancellation_exchange", "privacy_policy", "shipping_delivery", "contact_us", "home_page", "about_us", "catalogue"]
            social_media_categories = ["instagram_page", "facebook_page", "youtube_page", "linkedin_page", "twitter_page", "pinterest_page", "x_page"]

            # Create a list of URLs from POLICY categories ONLY (exclude social media)
            # Use a set to avoid duplicates when URLs appear in multiple categories
            unique_urls_set = set()
            policy_urls_dict = {}

            for category, url_list in website_urls_dict.items():
                if category in policy_categories:
                    # Include policy URLs for hard classification
                    unique_urls_set.update(url_list)  # Use set to avoid duplicates
                    policy_urls_dict[category] = url_list
                    self.logger.info(f"Including policy category '{category}' in hard classification", {"url_count": len(url_list)})
                elif category in social_media_categories:
                    # Skip social media URLs - they should not go through hard classification
                    self.logger.info(f"Skipping social media category '{category}' from hard classification", {"url_count": len(url_list), "reason": "social_media_uses_soft_classification_only"})
                else:
                    # Include other non-social media categories
                    unique_urls_set.update(url_list)  # Use set to avoid duplicates
                    policy_urls_dict[category] = url_list
                    self.logger.info(f"Including other category '{category}' in hard classification", {"url_count": len(url_list)})

            # Convert set to list for processing
            urls_list = list(unique_urls_set)

            # IMPORTANT: Apply URL limit BEFORE creating the mapping to prevent API errors
            MAX_URLS_HARD_CLASSIFICATION = 19
            if len(urls_list) > MAX_URLS_HARD_CLASSIFICATION:
                self.logger.warning("Policy URLs exceed hard classification limit - trimming to maximum", {
                    "original_count": len(urls_list),
                    "max_allowed": MAX_URLS_HARD_CLASSIFICATION,
                    "website": self.website,
                    "excluded_social_media": True,
                    "note": "URLs deduplicated to preserve multi-category assignments"
                })
                # Keep only the first 19 URLs to respect API limits
                urls_list = urls_list[:MAX_URLS_HARD_CLASSIFICATION]
                self.logger.info("Hard classification URLs limited successfully", {
                    "final_count": len(urls_list),
                    "policy_only": True,
                    "deduplicated": True
                })

            # Create a mapping of integers to URLs (now limited to ≤19 and policy URLs only)
            url_mapping = {i: url for i, url in enumerate(urls_list)}
            priority_categories = ["terms_and_condition", "returns_cancellation_exchange", "privacy_policy", "shipping_delivery", "contact_us", "home_page", "about_us", "catalogue", "pricing"]
            

            # Create a dictionary with integer indices instead of URLs for the prompt (policy URLs only)
            indices_dict = {}
            for category in priority_categories:
                if category in policy_urls_dict:  # Use filtered policy URLs dict
                    indices_dict[category] = []
                    for url in policy_urls_dict[category][:MAX_URLS_HARD_CLASSIFICATION]:
                        for idx, mapped_url in url_mapping.items():
                            if url == mapped_url:
                                indices_dict[category].append(idx)
                                break
            
            max_retries = 3
            retry_count = 0
            verified_indices_dict = None
            
            while retry_count < max_retries:
                try:
                    # Get the hard classification prompt (url_mapping is now guaranteed to be ≤20 URLs)
                    prompt = GptPromptPicker.gemini_25_flash_hard_classification_prompt(self.website, url_mapping)
                    
                    # Get response from Gemini
                    response = self.get_gemini_response_hard_classification(prompt)

                    self.logger.info("Gemini response received for hard classification", {
                        "response_length": len(response) if response else 0,
                        "response_preview": response[:200] + "..." if response and len(response) > 200 else response
                    })

                    self.logger.info("Parsing hard classification response", {
                        "response": response
                    })
                    
                    # Parse the response
                    verified_indices_dict = self.parse_openai_response(response)  # Same parsing logic

                    
                    self.logger.info("Verified indices dictionary created", {
                        "indices_categories": list(verified_indices_dict.keys()) if isinstance(verified_indices_dict, dict) else "invalid_format",
                        "total_indices": sum(len(v) for v in verified_indices_dict.values()) if isinstance(verified_indices_dict, dict) else 0
                    })
                    
                    # Enhanced logging for unreachable URLs detection
                    if isinstance(verified_indices_dict, dict):
                        unreachable_tool_count = len(verified_indices_dict.get("Unreachable_via_tool", []))
                        unreachable_count = len(verified_indices_dict.get("urls_not_reachable", [])) + unreachable_tool_count
                        reachable_count = sum(len(v) for k, v in verified_indices_dict.items() if k not in ["urls_not_reachable", "Unreachable_via_tool"] and isinstance(v, list))
                        
                        self.logger.info("Hard classification reachability results", {
                            "unreachable_urls_count": unreachable_count,
                            "reachable_urls_count": reachable_count,
                            "unreachable_urls": (verified_indices_dict.get("urls_not_reachable", []) + verified_indices_dict.get("Unreachable_via_tool", []))[:5],  # First 5 for debugging
                            "website": self.website,
                            "backup_flow_trigger": unreachable_tool_count > 0  # Simplified to match actual trigger
                        })

                        self.logger.info(
                            "Verified indices dictionary created", {
                            "indices_categories": list(verified_indices_dict.keys()) if isinstance(verified_indices_dict, dict) else "invalid_format",
                            "total_indices": sum(len(v) for v in verified_indices_dict.values()) if isinstance(verified_indices_dict, dict) else 0
                            }
                        )
                    
                    # Check if result is valid dictionary
                    if isinstance(verified_indices_dict, dict):
                        break
                    else:
                        self.logger.warning(
                            f"Hard classification returned non-dictionary on attempt {retry_count+1}",
                            {"result": verified_indices_dict},
                        )
                        retry_count += 1
                        time.sleep(5)  # Wait before retry
                except Exception as e:
                    self.logger.error(
                        f"Error in hard classification on attempt {retry_count+1}", error=e
                    )
                    retry_count += 1
                    time.sleep(5)  # Wait before retry
            
            # If all retries failed, return original soft classification
            if not isinstance(verified_indices_dict, dict):
                self.logger.error("All hard classification attempts failed, returning soft classification results")
                return website_urls_dict
            
            # Convert the indices back to URLs
            verified_urls_dict = self.convert_indices_to_urls(verified_indices_dict, url_mapping)

            # FIXED: Merge social media URLs back from soft classification (they were excluded from hard classification)
            social_media_categories = ["instagram_page", "facebook_page", "youtube_page", "linkedin_page", "twitter_page", "pinterest_page", "x_page"]
            for category in social_media_categories:
                if category in website_urls_dict and website_urls_dict[category]:
                    verified_urls_dict[category] = website_urls_dict[category]
                    self.logger.info(f"Merged social media category '{category}' from soft classification", {
                        "url_count": len(website_urls_dict[category]),
                        "reason": "social_media_skipped_hard_classification"
                    })

            # ENHANCED: Merge Twitter and X categories into single X category
            twitter_urls = verified_urls_dict.get("twitter_page", [])
            x_urls = verified_urls_dict.get("x_page", [])

            if twitter_urls or x_urls:
                # Combine both Twitter and X URLs into X category
                combined_urls = []
                if x_urls:
                    combined_urls.extend(x_urls)
                if twitter_urls:
                    combined_urls.extend(twitter_urls)

                # Remove duplicates while preserving order
                seen = set()
                unique_combined_urls = []
                for url in combined_urls:
                    if url not in seen:
                        seen.add(url)
                        unique_combined_urls.append(url)

                # Update the verified_urls_dict
                verified_urls_dict["x_page"] = unique_combined_urls

                # Remove the separate twitter_page category
                if "twitter_page" in verified_urls_dict:
                    del verified_urls_dict["twitter_page"]

                self.logger.info("🔄 Merged Twitter and X URLs into single X category", {
                    "twitter_urls_count": len(twitter_urls),
                    "x_urls_count": len(x_urls),
                    "combined_unique_count": len(unique_combined_urls),
                    "final_category": "x_page"
                })

            self.logger.info("Verified URLs dictionary created with social media merged", {
                "verified_categories": list(verified_urls_dict.keys()),
                "category_counts": {k: len(v) for k, v in verified_urls_dict.items() if isinstance(v, list)},
                "total_verified_urls": sum(len(v) for v in verified_urls_dict.values() if isinstance(v, list)),
                "social_media_merged": True
            })

            self.logger.info(
                "Completed hard classification verification with social media merge",
                {
                    "verified_categories": list(verified_urls_dict.keys()),
                    "total_verified_urls": sum(len(urls) for urls in verified_urls_dict.values() if isinstance(urls, list)),
                    "policy_hard_classified": True,
                    "social_media_soft_classified": True
                }
            )
            
            # Save hard classification results to database
            try:
                self.logger.info("Saving hard classification results to database")
                self.update_db_with_hard_classification(verified_urls_dict)
                self.logger.info("Successfully saved hard classification results to database")
            except Exception as db_error:
                self.logger.error(f"Error saving hard classification results to database: {db_error}")
                # Don't fail the entire process, just log the error
            
            return verified_urls_dict
            
        except Exception as e:
            self.logger.error("Error in hard classification", error=e)
            traceback.print_exc()
            return website_urls_dict  # Return original if error
            
    def soft_classify_urls(self, urls1, urls2, logger, scrape_request_ref_id, org_id):
        """Perform soft classification of URLs"""
        self.logger.info(
            "Starting soft classification",
            {"website": self.website, "urls_depth_1_count": len(urls1), "urls_depth_2_count": len(urls2)},
        )

        output_df = pd.DataFrame(columns=["website", "url", "priority_url", "soft_class"])
        mcc_dict = {}

        try:
            df = self.prepare_dataframe(self.website, urls1, urls2)
            website = df["website"][0]
            dictionary_1 = df["dictionary_1"][0]

            max_retries = 3
            retry_count = 0
            policy_urls_dict = None

            while retry_count < max_retries:
                try:
                    self.logger.info("Dictionary for soft classification prepared", {
                        "url_count": len(dictionary_1),
                        "sample_urls": list(dictionary_1.values())[:3]
                    })
                    # Get the prompt using the correct method
                    prompt = GptPromptPicker.get_gpt_4o_soft_classification_prompt(website, dictionary_1)

                    # Get response from OpenAI
                    response = self.get_openai_response(prompt)
                    
                    self.logger.info("Soft classification response received", {
                        "response_length": len(response) if response else 0,
                        "response_preview": response[:200] + "..." if response and len(response) > 200 else response
                    })
                    # Parse the response
                    policy_urls_dict = self.parse_openai_response(response)

                    # self.logger.info(f"Policy URLs dictionary: {policy_urls_dict}")
                    # Check if result is valid dictionary
                    if isinstance(policy_urls_dict, dict):
                        break
                    else:
                        self.logger.warning(
                            f"Policy URLs classification returned non-dictionary on attempt {retry_count+1}",
                            {"result": policy_urls_dict},
                        )
                        retry_count += 1
                        time.sleep(5)  # Wait before retry
                except Exception as e:
                    self.logger.error(
                        f"Error in policy URLs classification on attempt {retry_count+1}", error=e
                    )
                    retry_count += 1
                    time.sleep(5)  # Wait before retry

            # If all retries failed, provide empty default dictionary
            if not isinstance(policy_urls_dict, dict):
                self.logger.error("All policy URL classification attempts failed, using empty defaults")
                policy_urls_dict = {
                    "home_page": [0] if len(dictionary_1) > 0 else [],
                    "about_us": [],
                    "terms_and_condition": [],
                    "returns_cancellation_exchange": [],
                    "privacy_policy": [],
                    "shipping_delivery": [],
                    "contact_us": [],
                    "products": [],
                    "services": [],
                    "catalogue": [],
                    "pricing": [],
                    "faq": [],
                    "instagram_page": [],
                    "facebook_page": [],
                    "youtube_page": [],
                    "twitter_page": [],
                    "linkedin_page": [],
                    "pinterest_page": [],
                }

            # Log before processing results
            total_chars = sum(len(url) for url in dictionary_1.values())
            self.logger.info(
                "Preparing data for model policy result",
                {
                    "website": website,
                    "dictionary_1_count": len(dictionary_1),
                    "total_chars": total_chars,
                    "urls_sample": list(dictionary_1.values())[:3],
                },
            )

            self.logger.info(
                "Got policy URLs classification",
                {"policy_urls": policy_urls_dict, "categories_found": list(policy_urls_dict.keys())},
            )

            # Create output DataFrame
            urls_list = list(dictionary_1.values())
            output_df = pd.DataFrame({
                "website": [website] * len(urls_list),
                "url": urls_list,
                "priority_url": [False] * len(urls_list),
                "soft_class": [[] for _ in range(len(urls_list))]
            })

            # Process classification results
            for category, url_indices in policy_urls_dict.items():
                mcc_dict[category] = []
                for url_i in url_indices:
                    if url_i in dictionary_1:
                        url = dictionary_1[url_i]
                        mcc_dict[category].append(url)
                        
                        # Update the dataframe
                        mask = output_df["url"] == url
                        if mask.any():
                            current_classes = output_df.loc[mask, "soft_class"].iloc[0]
                            if isinstance(current_classes, list):
                                current_classes.append(category)
                            else:
                                current_classes = [category]
                            output_df.loc[mask, "soft_class"] = [current_classes]
                            
                            if category in priority_categories:
                                output_df.loc[mask, "priority_url"] = True

            self.logger.info(
                "Completed soft classification",
                {
                    "mcc_dict": mcc_dict,
                    "total_classified_urls": len(output_df[output_df["soft_class"].apply(len) > 0]) if not output_df.empty else 0,
                    "priority_urls_count": len(output_df[output_df["priority_url"] == True]) if not output_df.empty else 0,
                },
            )

        except Exception as e:
            self.logger.error("Error in soft classification", error=e)
            traceback.print_exc()

        return output_df, mcc_dict

def get_urls_by_depth(json_file_path, logger):
    """Extract URLs from the JSON file"""
    try:
        # Create a simple service instance just for loading the JSON
        service = urlclassification_service()
        data = service.load_json_data(json_file_path)
        if not data:
            return [], []
        
        # Extract URLs from the parsed_urls structure
        urls_depth_1 = []
        urls_depth_2 = []
        
        if "parsed_urls" in data and len(data["parsed_urls"]) > 0:
            for parsed_url_obj in data["parsed_urls"]:
                if "urls" in parsed_url_obj:
                    if parsed_url_obj.get("url_depth") == 1:
                        urls_depth_1 = parsed_url_obj["urls"]
                    elif parsed_url_obj.get("url_depth") == 2:
                        urls_depth_2 = parsed_url_obj["urls"]
        
        logger.info("Retrieved URLs by depth", {"depth_1_urls_count": len(urls_depth_1), "depth_2_urls_count": len(urls_depth_2)})
        
        return urls_depth_1, urls_depth_2

    except Exception as e:
        logger.error("Error retrieving URLs by depth", error=e)
        return [], []

def main():
    """Main function to run the classification"""
    # Initialize logger
    logger = SimpleLogger("test_analysis")
    
    # Load JSON data
    json_file_path = "test_jsons/lilybelle.json"
    service = urlclassification_service()
    data = service.load_json_data(json_file_path)
    
    if not data:
        logger.error("Failed to load data")
        return
    
    # Extract website and URLs
    website = data.get("website", "test_jsons/academicfight.json")
    urls1, urls2 = get_urls_by_depth(json_file_path, logger)
    
    if not urls1:
        logger.error("No URLs found to classify")
        return
    
    logger.info("Starting URL classification", {
        "website": website,
        "depth_1_urls": len(urls1),
        "depth_2_urls": len(urls2)
    })
    
    # Run soft classification
    logger.info("=" * 50)
    logger.info("STARTING SOFT CLASSIFICATION")
    logger.info("=" * 50)
    service = urlclassification_service(website=website)
    output_df, website_urls_dict = service.soft_classify_urls(
        urls1, urls2, logger, "test_ref_id", "default"
    )
    
    # Run hard classification verification
    logger.info("\n" + "="*50)
    logger.info("STARTING HARD CLASSIFICATION VERIFICATION")
    logger.info("="*50)
    verified_urls_dict = service.hard_classify_urls(website_urls_dict)
    
    # Display results
    logger.info("=" * 50)
    logger.info("SOFT CLASSIFICATION RESULTS")
    logger.info("=" * 50)
    
    logger.info("Soft classification summary", {
        "total_classified": len(output_df),
        "priority_urls": len(output_df[output_df['priority_url'] == True]) if not output_df.empty else 0
    })
    
    logger.info("Category breakdown:")
    for category, urls in website_urls_dict.items():
        if urls:
            category_info = {
                "category": category,
                "url_count": len(urls),
                "sample_urls": urls[:3]
            }
            if len(urls) > 3:
                category_info["additional_urls"] = len(urls) - 3
            logger.info("Category details", category_info)
    
    # Display hard classification results
    logger.info("\n" + "="*50)
    logger.info("HARD CLASSIFICATION RESULTS (VERIFIED)")
    logger.info("="*50)
    
    logger.info("Verified category breakdown:")
    for category, urls in verified_urls_dict.items():
        if urls:
            category_info = {
                "category": category,
                "url_count": len(urls),
                "sample_urls": urls[:3]
            }
            if len(urls) > 3:
                category_info["additional_urls"] = len(urls) - 3
            logger.info("Verified category details", category_info)
    
    # Compare soft vs hard classification
    logger.info("=" * 50)
    logger.info("CLASSIFICATION COMPARISON")
    logger.info("=" * 50)
    
    for category in website_urls_dict.keys():
        soft_count = len(website_urls_dict.get(category, []))
        hard_count = len(verified_urls_dict.get(category, []))
        if soft_count != hard_count:
            comparison_info = {
                "category": category,
                "soft_count": soft_count,
                "hard_count": hard_count,
                "change": hard_count - soft_count
            }
            logger.info("Classification change detected", comparison_info)
    
    # Save results to file
    output_df.to_csv("/home/<USER>/Desktop/final/classification_results.csv", index=False)
    
    with open("/home/<USER>/Desktop/final/soft_classification_results.json", "w") as f:
        json.dump(website_urls_dict, f, indent=2)
        
    with open("/home/<USER>/Desktop/final/hard_classification_results.json", "w") as f:
        json.dump(verified_urls_dict, f, indent=2)
    
    logger.info("Results saved successfully", {
        "files": [
            "classification_results.csv",
            "soft_classification_results.json", 
            "hard_classification_results.json"
        ]
    })

# Create alias for backward compatibility
UrlClassificationService = urlclassification_service

if __name__ == "__main__":
    main()